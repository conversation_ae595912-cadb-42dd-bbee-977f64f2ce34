import { useEffect, useState } from 'react'

export default function Portal() {
  const [mounted, setMounted] = useState(false)
  const [selectedAgent, setSelectedAgent] = useState(null)
  const [config, setConfig] = useState(null)

  const agents = [
    {
      id: 'customer-service',
      name: '客服助手',
      description: '专业的客户服务支持',
      difyUrl: 'http://************:9009/chatbot/05VnYqM6Gz8fbnrl'
    },
    {
      id: 'sales-assistant',
      name: '销售顾问',
      description: '产品咨询和销售支持',
      difyUrl: 'http://************:9009/chatbot/05VnYqM6Gz8fbnrl'
    }
  ]

  useEffect(() => {
    setMounted(true)

    // 监听来自父窗口的消息
    const handleMessage = (event) => {
      console.log('Portal received message:', event.data)
      if (event.data.type === 'ai-agent-config') {
        console.log('Received config:', event.data.config)
        setConfig(event.data.config)
      }
    }

    window.addEventListener('message', handleMessage)

    // 通知父窗口iframe已准备就绪
    if (window.parent !== window) {
      console.log('Portal sending ready message to parent')
      window.parent.postMessage({
        type: 'ai-agent-ready'
      }, '*')
    }

    // 添加全局错误监听
    const handleError = (event) => {
      console.error('Portal global error:', event.error)
    }

    window.addEventListener('error', handleError)

    return () => {
      window.removeEventListener('message', handleMessage)
      window.removeEventListener('error', handleError)
    }
  }, [])

  const handleClose = () => {
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'ai-agent-close'
      }, '*')
    }
  }

  const selectAgent = (agent) => {
    setSelectedAgent(agent)
  }

  const goBack = () => {
    setSelectedAgent(null)
  }

  if (!mounted) {
    return <div>Loading...</div>
  }

  return (
    <>
      <style jsx global>{`
        .loading-spinner {
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
      <div style={{
        width: '100%',
        height: '100%',
        background: 'white',
        borderRadius: '0',
        boxShadow: 'none',
        display: 'flex',
        flexDirection: 'column',
        fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
        borderLeft: '1px solid #e5e7eb'
      }}>
      {/* Header */}
      <div style={{
        padding: '16px 20px',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h2 style={{ margin: 0, fontSize: '18px', color: '#1f2937' }}>
          AI Agent 助手
        </h2>
        <button
          onClick={handleClose}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '24px',
            cursor: 'pointer',
            color: '#6b7280',
            padding: 0,
            width: '24px',
            height: '24px'
          }}
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        {!selectedAgent ? (
          <AgentList agents={agents} onSelect={selectAgent} />
        ) : (
          <AgentChat agent={selectedAgent} onBack={goBack} />
        )}
      </div>
    </div>
    </>
  )
}

// Agent List Component
function AgentList({ agents, onSelect }) {
  return (
    <div style={{ padding: '20px' }}>
      <h3 style={{ margin: '0 0 16px 0', color: '#374151' }}>选择AI助手</h3>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        {agents.map(agent => (
          <div
            key={agent.id}
            onClick={() => onSelect(agent)}
            style={{
              padding: '16px',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.2s',
              backgroundColor: '#f9fafb'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#f3f4f6'
              e.target.style.borderColor = '#d1d5db'
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#f9fafb'
              e.target.style.borderColor = '#e5e7eb'
            }}
          >
            <h4 style={{ margin: '0 0 8px 0', color: '#1f2937' }}>{agent.name}</h4>
            <p style={{ margin: 0, color: '#6b7280', fontSize: '14px' }}>{agent.description}</p>
          </div>
        ))}
      </div>
    </div>
  )
}

// Agent Chat Component
function AgentChat({ agent, onBack }) {
  const [iframeLoaded, setIframeLoaded] = useState(false)
  const [iframeError, setIframeError] = useState(null)

  const handleIframeLoad = () => {
    console.log('Iframe loaded successfully:', agent.difyUrl)
    setIframeLoaded(true)
    setIframeError(null)
  }

  const handleIframeError = (error) => {
    console.error('Iframe load error:', error, 'URL:', agent.difyUrl)
    setIframeError('Failed to load Dify application')
  }

  // 添加iframe内容检查和智能加载检测
  useEffect(() => {
    let checkTimer
    let timeoutTimer

    const checkIframeContent = () => {
      try {
        const iframe = document.querySelector(`iframe[src="${agent.difyUrl}"]`)
        if (iframe && iframe.contentDocument) {
          const doc = iframe.contentDocument
          const loadingElement = doc.querySelector('.spin-animation')
          const chatElement = doc.querySelector('[data-testid="chat"], .chat-container, [class*="chat"]')

          if (!loadingElement && chatElement) {
            console.log('Dify app fully loaded - chat interface detected')
            setIframeLoaded(true)
            setIframeError(null)
            return true
          }
        }
      } catch (e) {
        // 跨域限制，无法访问iframe内容，依赖onLoad事件
        console.log('Cannot access iframe content due to CORS, relying on onLoad event')
      }
      return false
    }

    // 定期检查iframe内容
    if (!iframeLoaded && !iframeError) {
      checkTimer = setInterval(checkIframeContent, 2000)
    }

    // 超时处理
    timeoutTimer = setTimeout(() => {
      if (!iframeLoaded && !iframeError) {
        console.warn('Iframe taking too long to load:', agent.difyUrl)
        // 不设置错误，让用户继续等待
      }
    }, 15000) // 15秒超时

    return () => {
      if (checkTimer) clearInterval(checkTimer)
      if (timeoutTimer) clearTimeout(timeoutTimer)
    }
  }, [agent.difyUrl, iframeLoaded, iframeError])

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* Chat Header */}
      <div style={{
        padding: '12px 20px',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }}>
        <button
          onClick={onBack}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '18px',
            cursor: 'pointer',
            color: '#6b7280',
            padding: '4px'
          }}
        >
          ←
        </button>
        <h3 style={{ margin: 0, color: '#1f2937' }}>{agent.name}</h3>
        {!iframeLoaded && (
          <span style={{ fontSize: '12px', color: '#6b7280' }}>Loading...</span>
        )}
      </div>

      {/* Chat Content */}
      <div style={{
        flex: 1,
        position: 'relative',
        overflow: 'hidden',
        height: '100%',
        backgroundColor: '#f9fafb'
      }}>
        {iframeError && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            color: '#ef4444',
            padding: '20px'
          }}>
            <p>{iframeError}</p>
            <p style={{ fontSize: '14px', color: '#6b7280' }}>
              URL: {agent.difyUrl}
            </p>
            <button
              onClick={() => {
                setIframeError(null)
                setIframeLoaded(false)
              }}
              style={{
                padding: '8px 16px',
                background: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Retry
            </button>
          </div>
        )}

        <iframe
          key={`iframe-${agent.id}-${Date.now()}`}
          src={agent.difyUrl}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            border: '1px solid #e5e7eb',
            borderRadius: '0',
            backgroundColor: 'white',
            zIndex: 1
          }}
          allow="microphone; camera; geolocation; autoplay; encrypted-media; fullscreen"
          referrerPolicy="no-referrer-when-downgrade"
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          title={`${agent.name} Chat Interface`}
          loading="eager"
        />

        {/* Loading overlay */}
        {!iframeLoaded && !iframeError && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            color: '#6b7280',
            zIndex: 2,
            background: 'rgba(255,255,255,0.95)',
            padding: '30px',
            borderRadius: '12px',
            border: '1px solid #e5e7eb',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            maxWidth: '300px'
          }}>
            <div style={{
              marginBottom: '15px',
              fontSize: '16px',
              fontWeight: '500'
            }}>
              🤖 正在启动AI助手
            </div>
            <div style={{
              marginBottom: '15px',
              fontSize: '14px',
              color: '#374151'
            }}>
              正在加载Dify应用，请稍候...
            </div>
            <div style={{
              width: '40px',
              height: '40px',
              border: '3px solid #f3f4f6',
              borderTop: '3px solid #3b82f6',
              borderRadius: '50%',
              margin: '0 auto 15px'
            }} className="loading-spinner"></div>
            <div style={{ fontSize: '12px', color: '#9ca3af' }}>
              首次加载可能需要10-15秒
            </div>
          </div>
        )}

        {/* Debug info overlay */}
        <div style={{
          position: 'absolute',
          bottom: '10px',
          right: '10px',
          fontSize: '10px',
          color: '#9ca3af',
          background: 'rgba(255,255,255,0.8)',
          padding: '4px 8px',
          borderRadius: '4px',
          zIndex: 3
        }}>
          Status: {iframeLoaded ? 'Loaded' : iframeError ? 'Error' : 'Loading'}
        </div>
      </div>
    </div>
  )
}