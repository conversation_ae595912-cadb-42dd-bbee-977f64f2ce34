<template>
  <div class="agent-portal">
    <div class="portal-header">
      <h2>AI Agent 助手</h2>
      <button @click="$emit('close')" class="close-btn">×</button>
    </div>
    
    <div class="portal-content">
      <AgentList 
        v-if="!selectedAgent" 
        :agents="agents" 
        @select="selectAgent" 
      />
      
      <AgentChat 
        :agent="selectedAgent" 
        @back="selectedAgent = null" 
      />
    </div>
  </div>
</template>

<script>
import AgentList from './AgentList.vue'
import AgentChat from './AgentChat.vue'

export default {
  name: 'AgentPortal',
  components: {
    AgentList,
    AgentChat
  },
  data() {
    return {
      selectedAgent: null,
      agents: [
          {
            id: 'customer-service',
            name: '客服助手',
            description: '专业的客户服务支持',
            difyUrl: 'http://************:9009/chatbot/05VnYqM6Gz8fbnrl'
          },
          {
            id: 'sales-assistant',
            name: '销售顾问',
            description: '产品咨询和销售支持',
            difyUrl: 'http://************:9009/chatbot/05VnYqM6Gz8fbnrl'
          }
      ]
    }
  },
  methods: {
    selectAgent(agent) {
      this.selectedAgent = agent
    }
  }
}
</script>

<style scoped>
.agent-portal {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

.portal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.portal-header h2 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 24px;
  height: 24px;
}

.portal-content {
  flex: 1;
  overflow: hidden;
}
</style>