<template>
  <div class="agent-chat">
    <div class="chat-header">
      <button @click="$emit('back')" class="back-btn">←</button>
      <div class="agent-info">
        <span class="agent-icon">🤖</span>
        <span class="agent-name">{{ agent.name }}</span>
      </div>
    </div>
    
    <div class="chat-content">
      <iframe 
        :src="agent.difyUrl"
        frameborder="0"
        allow="microphone"
        class="dify-iframe"
      ></iframe>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AgentChat',
  props: {
    agent: {
      type: Object,
      required: true
    }
  },
  emits: ['back']
}
</script>

<style scoped>
.agent-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.agent-icon {
  font-size: 16px;
}

.agent-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.chat-content {
  flex: 1;
  position: relative;
}

.dify-iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>